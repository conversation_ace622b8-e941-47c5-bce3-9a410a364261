import { NextRequest, NextResponse } from 'next/server';
import { pydanticAIService } from '@/lib/pydantic-ai-service';

export async function POST(request: NextRequest) {
  try {
    const loweryFinishingInput = `
Below are some notes for you to be able to get started. Let me know if you have any questions or need any additional information.

1.) Website
  - current website (click funnel) https://www.loweryfinishing.com/optin-604655891694725683997
  - I would like to have a legit website that would make it even easier for customers to request quotes. I also feel it could be designed better to find the work that I want to do and not be so broad. I'll explain the work I want to find further in this email.

2.) Google listing
  - I have a few good reviews and try to get them each time we do a job for a customer. Usually we are doing work for builders and don't ask for a review. But should we? Is there a better way to show credibility with big builders?
  - One of the pictures from a review automatically shows up with the listing which is kind of annoying...Not sure how to fix this.

3.) Goals
  A. Homeowners
      - exterior door swaps. Not repairs, or anything else. Just straight swaps. This is one I would like to highlight in particular. Good money maker for us. I would also like to charge a fee to bid if we need to come out to take measurements. Would be nice to have a system with instructions on how to give us information so we can give them an easy free estimate without driving to the job.
      - Everything else listed under (B) as well.

 B.) Builders (apartments, custom homes, track homes)
      We Install the following below:
Exterior Doors
Interior Doors
Pocket doors
Bi Fold Doors
Bi Pass Doors
Barndoor
Transoms
Cased Opening
Window surrounds
Window sills
Window mulls
Closet Shelf
Closet Shelf	face framed
Closet Linen
Closet Linen	face framed
Base
Base Shoe
Treads & Risers
Skirt Boards
Wall cap
Handrail
Door, and bath hardware
Attic access doors
Open Rail
Newel Post
Deco Shelf
Mantel
Built in bookcases
Built in bench
Wainscot
built in desk / sub top for counter
built in Lockers
Setting Exterior Doors
Setting Pocket Door frames
`;

    console.log('🧪 Testing complete Lowery Finishing proposal creation...');

    // Test the AI service
    const proposal = await pydanticAIService.generateProposal(loweryFinishingInput);

    // Test the enhanced package generation
    const analysis = await pydanticAIService.analyzeCustomer(loweryFinishingInput);

    // Test the enhanced package generation function
    function createEnhancedPackagesFromAI(aiProposalData: any, businessName: string) {
      const recommendedPackage = aiProposalData.packageRecommendation?.recommended || 'basic';

      return [
        {
          name: 'Basic "GetFound" Package',
          description: `Designed to establish a strong online foundation for ${businessName} and leverage our unique content app. This package provides everything you need to create a professional web presence that showcases your work and makes it easy for customers to find and contact you.`,
          features: aiProposalData.customizedFeatures?.basic || [
            `Custom 5-Page Professional Website designed specifically for ${businessName}`,
            `GetFound Mobile App Integration - easily capture and showcase your completed projects`,
            `Filterable Service Portfolio - let customers see examples of your specific work`,
            `Mobile-Responsive Design ensuring your site looks perfect on all devices`,
            `Basic SEO Setup & Optimization to help customers find ${businessName} online`,
            `Professional Quote Request Forms streamlined for your services`,
            `Website Hosting & Maintenance - we handle all the technical details`,
            `Google Business Profile Review & Optimization recommendations`
          ],
          setupFee: '$499',
          monthlyFee: '$100',
          isRecommended: recommendedPackage === 'basic',
          howItHelps: `This package provides ${businessName} with a professional website that builds credibility and makes it easy for customers to request quotes. The GetFound app integration means you can effortlessly showcase completed projects, building trust with potential customers who can see your actual work. The mobile-responsive design ensures your site looks great on all devices, and basic SEO helps customers find you online.`,
          businessExamples: [
            "Professional 5-page website that builds immediate credibility with potential customers",
            "Easy content creation through the GetFound app - just take photos and record voice notes after each job",
            "Filterable portfolio that lets customers see examples of your specific services"
          ],
          paymentBreakdown: {
            upfront: '$499',
            monthly: '$100/month',
            amortized: '$141.58/month for 12 months, then $100/month'
          }
        },
        {
          name: 'Premium "GetFound" SEO & Growth Package',
          description: `Our most comprehensive solution, designed for maximum visibility, targeted SEO, and enhanced lead generation for ${businessName}. This package strategically structures your website content to rank higher in search results and attract your ideal customers.`,
          features: aiProposalData.customizedFeatures?.premium || [
            `Comprehensive 10-15+ Page Website with dedicated service pages for ${businessName}`,
            `Advanced SEO Strategy with keyword research specific to your market area`,
            `Smart AI Content Builder - automatically places your GetFound app content on relevant service pages`,
            `Individual Service Pages that rank separately in search results`,
            `Enhanced Lead Generation System with specialized quote forms`,
            `Keyword Rank Tracking Dashboard (launching September 2025)`,
            `Comprehensive Google Business Profile Management and optimization`,
            `Local SEO Optimization targeting customers in your service area`,
            `Professional Content Strategy designed for your specific business type`,
            `Advanced Analytics and Performance Monitoring`
          ],
          setupFee: '$2,999',
          monthlyFee: '$150',
          isRecommended: recommendedPackage === 'premium',
          howItHelps: `This comprehensive package transforms ${businessName} into a search engine powerhouse. With dedicated pages for each service, advanced SEO strategy, and smart content placement, you'll rank higher for specific searches in your area. The enhanced lead generation system and professional presentation help you compete with larger companies while the rank tracking keeps you informed of your progress.`,
          businessExamples: [
            "Dedicated service pages that rank individually in search results, bringing in more targeted leads",
            "Advanced SEO strategy that helps you compete with larger companies in your market",
            "Smart AI content placement that automatically strengthens your most important service pages",
            "Rank tracking dashboard to monitor your search engine performance and ROI"
          ],
          paymentBreakdown: {
            upfront: '$2,999',
            monthly: '$150/month',
            amortized: '$399.92/month for 12 months, then $150/month'
          }
        }
      ];
    }

    // Create enhanced packages like the API would
    const packages = createEnhancedPackagesFromAI(proposal, 'Lowery Finishing');

    return NextResponse.json({
      success: true,
      message: 'Complete Lowery Finishing AI proposal test',
      proposal: proposal,
      analysis: analysis,
      packages: packages,
      inputLength: loweryFinishingInput.length,
      questionsCount: proposal.questionsAndAnswers?.length || 0
    });

  } catch (error) {
    console.error('Test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST to test Lowery Finishing proposal generation'
  });
}
