/**
 * AI-powered dynamic proposal generation service
 * Integrates OpenAI with the dynamic proposal generator for real-time customization
 */

import { generateDynamicProposal, DynamicProposalContent, CustomerAnalysis } from './dynamic-proposal-generator';
import { EnhancedProposal } from '@/types/enhanced-proposal-schema';

export interface AIProposalRequest {
  customerInfo: string;
  businessName?: string;
  businessType?: string;
  existingProposal?: EnhancedProposal;
  specificQuestions?: string[];
}

export interface AIProposalResponse {
  dynamicContent: DynamicProposalContent;
  updatedProposal: EnhancedProposal;
  analysis: CustomerAnalysis;
  aiEnhancements: {
    improvedGreeting: string;
    enhancedSolutions: string[];
    customizedNextSteps: string[];
  };
}

/**
 * Generates a complete dynamic proposal using AI enhancement
 */
export async function generateAIEnhancedProposal(request: AIProposalRequest): Promise<AIProposalResponse> {
  // Step 1: Generate base dynamic content
  const dynamicContent = generateDynamicProposal(request.customerInfo);
  
  // Step 2: Use AI to enhance the content
  const aiEnhancements = await enhanceProposalWithAI(dynamicContent, request);
  
  // Step 3: Create or update the enhanced proposal
  const updatedProposal = createEnhancedProposalFromDynamic(
    dynamicContent, 
    request.existingProposal,
    aiEnhancements
  );
  
  // Step 4: Extract analysis for reference
  const analysis = extractAnalysisFromDynamic(dynamicContent);
  
  return {
    dynamicContent,
    updatedProposal,
    analysis,
    aiEnhancements
  };
}

/**
 * Uses OpenAI to enhance the generated proposal content
 */
async function enhanceProposalWithAI(
  dynamicContent: DynamicProposalContent, 
  request: AIProposalRequest
): Promise<{
  improvedGreeting: string;
  enhancedSolutions: string[];
  customizedNextSteps: string[];
}> {
  const prompt = `
You are an expert proposal writer for GetFound, a digital marketing company specializing in local businesses.

CONTEXT:
- Business: ${request.businessName || 'the client'}
- Type: ${request.businessType || 'local business'}
- Current greeting: ${dynamicContent.clientGreeting}
- Current solutions: ${dynamicContent.specificSolutions.join(', ')}
- Customer info: ${request.customerInfo}

TASK: Enhance this proposal content to be more personalized and compelling.

1. IMPROVE THE GREETING:
Make it more personal and specific to their business challenges. Reference specific details from their input.

2. ENHANCE SOLUTIONS:
Add 2-3 more specific solutions based on their unique situation. Be concrete and actionable.

3. CUSTOMIZE NEXT STEPS:
Make the next steps more specific to their business type and goals.

REQUIREMENTS:
- Keep the professional tone
- Be specific to their business type
- Reference their actual challenges/goals
- Make it feel personally written for them
- Stay focused on GetFound's services

Respond in JSON format:
{
  "improvedGreeting": "enhanced greeting text",
  "enhancedSolutions": ["solution 1", "solution 2", "solution 3"],
  "customizedNextSteps": ["step 1", "step 2", "step 3", "step 4"]
}
`;

  try {
    // For now, return enhanced content based on the dynamic analysis
    // TODO: Replace with actual OpenAI API call
    return {
      improvedGreeting: enhanceGreeting(dynamicContent, request),
      enhancedSolutions: enhanceSolutions(dynamicContent, request),
      customizedNextSteps: enhanceNextSteps(dynamicContent, request)
    };
  } catch (error) {
    console.error('AI enhancement failed, using dynamic content:', error);
    return {
      improvedGreeting: dynamicContent.clientGreeting,
      enhancedSolutions: dynamicContent.specificSolutions,
      customizedNextSteps: dynamicContent.nextSteps
    };
  }
}

/**
 * Creates an EnhancedProposal from dynamic content
 */
function createEnhancedProposalFromDynamic(
  dynamicContent: DynamicProposalContent,
  existingProposal?: EnhancedProposal,
  aiEnhancements?: any
): EnhancedProposal {
  const base = existingProposal || {
    clientName: '',
    businessName: '',
    businessType: '',
    packages: []
  };

  return {
    ...base,
    overview: {
      projectTitle: dynamicContent.projectTitle,
      introduction: aiEnhancements?.improvedGreeting || dynamicContent.clientGreeting,
      summary: dynamicContent.situationAnalysis
    },
    clientGoals: {
      title: "Understanding Your Goals",
      description: dynamicContent.goalsSection,
      goals: extractGoalsFromContent(dynamicContent.goalsSection)
    },
    packages: createPackagesFromDynamic(dynamicContent),
    specificQuestions: {
      title: "Addressing Your Specific Questions",
      questions: dynamicContent.questionsAndAnswers.map(qa => ({
        question: qa.question,
        answer: qa.answer
      }))
    },
    nextSteps: aiEnhancements?.customizedNextSteps || dynamicContent.nextSteps
  };
}

/**
 * Creates enhanced package data from dynamic content with detailed descriptions
 */
function createPackagesFromDynamic(dynamicContent: DynamicProposalContent) {
  const packages = [
    {
      name: 'Basic "GetFound" Package',
      description: generateBasicPackageDescription(dynamicContent),
      features: dynamicContent.customizedFeatures.basic,
      setupFee: '$499',
      monthlyFee: '$100',
      isRecommended: dynamicContent.packageRecommendation.recommended === 'basic',
      howItHelps: generateBasicHowItHelps(dynamicContent),
      businessExamples: generateBasicBusinessExamples(dynamicContent),
      paymentBreakdown: {
        upfront: '$499',
        monthly: '$100/month',
        amortized: '$141.58/month for 12 months, then $100/month'
      }
    },
    {
      name: 'Premium "GetFound" SEO & Growth Package',
      description: generatePremiumPackageDescription(dynamicContent),
      features: dynamicContent.customizedFeatures.premium,
      setupFee: '$2,999',
      monthlyFee: '$150',
      isRecommended: dynamicContent.packageRecommendation.recommended === 'premium',
      howItHelps: generatePremiumHowItHelps(dynamicContent),
      businessExamples: generatePremiumBusinessExamples(dynamicContent),
      paymentBreakdown: {
        upfront: '$2,999',
        monthly: '$150/month',
        amortized: '$399.92/month for 12 months, then $150/month'
      }
    }
  ];

  return packages;
}

/**
 * Extracts goals from the goals section text
 */
function extractGoalsFromContent(goalsSection: string): string[] {
  const lines = goalsSection.split('\n');
  return lines
    .filter(line => line.trim().startsWith('•'))
    .map(line => line.replace('•', '').trim());
}

/**
 * Extracts analysis data from dynamic content
 */
function extractAnalysisFromDynamic(dynamicContent: DynamicProposalContent): CustomerAnalysis {
  return {
    businessName: extractBusinessNameFromTitle(dynamicContent.projectTitle),
    businessType: 'local business', // This would be extracted from the analysis
    businessCategory: 'service',
    currentSituation: dynamicContent.situationAnalysis,
    targetMarkets: ['local customers'],
    keyGoals: extractGoalsFromContent(dynamicContent.goalsSection),
    challenges: [],
    businessMaturity: 'growing',
    recommendedPackage: dynamicContent.packageRecommendation.recommended,
    confidence: 0.8,
    reasoning: dynamicContent.packageRecommendation.whyThisPackage
  };
}

function extractBusinessNameFromTitle(title: string): string {
  const match = title.match(/for (.+)$/);
  return match ? match[1] : 'Your Business';
}

// Enhanced content functions (temporary until OpenAI integration)
function enhanceGreeting(dynamicContent: DynamicProposalContent, request: AIProposalRequest): string {
  return dynamicContent.clientGreeting + " We've carefully reviewed your specific needs and challenges to create this customized proposal.";
}

function enhanceSolutions(dynamicContent: DynamicProposalContent, request: AIProposalRequest): string[] {
  return [
    ...dynamicContent.specificSolutions,
    "Dedicated project manager to ensure smooth implementation",
    "Regular progress updates and milestone check-ins",
    "Post-launch optimization and performance monitoring"
  ];
}

function enhanceNextSteps(dynamicContent: DynamicProposalContent, request: AIProposalRequest): string[] {
  return [
    "Schedule a 30-minute discovery call to discuss your specific requirements",
    "Review and select the package that best fits your business goals",
    "Complete the project agreement and initial setup payment",
    "Begin the onboarding process with our specialized team",
    "Launch your new professional online presence within 2-3 weeks"
  ];
}

/**
 * Generate detailed package descriptions and explanations
 */
function generateBasicPackageDescription(dynamicContent: DynamicProposalContent): string {
  const businessName = extractBusinessNameFromContent(dynamicContent);
  return `Designed to establish a strong online foundation for ${businessName} and leverage our unique content app. This package provides everything you need to create a professional web presence that showcases your work and makes it easy for customers to find and contact you.`;
}

function generatePremiumPackageDescription(dynamicContent: DynamicProposalContent): string {
  const businessName = extractBusinessNameFromContent(dynamicContent);
  return `Our most comprehensive solution, designed for maximum visibility, targeted SEO, and enhanced lead generation for ${businessName}. This package strategically structures your website content to rank higher in search results and attract your ideal customers.`;
}

function generateBasicHowItHelps(dynamicContent: DynamicProposalContent): string {
  const businessName = extractBusinessNameFromContent(dynamicContent);
  return `This package provides ${businessName} with a professional website that builds credibility and makes it easy for customers to request quotes. The GetFound app integration means you can effortlessly showcase completed projects, building trust with potential customers who can see your actual work. The mobile-responsive design ensures your site looks great on all devices, and basic SEO helps customers find you online.`;
}

function generatePremiumHowItHelps(dynamicContent: DynamicProposalContent): string {
  const businessName = extractBusinessNameFromContent(dynamicContent);
  return `This comprehensive package transforms ${businessName} into a search engine powerhouse. With dedicated pages for each service, advanced SEO strategy, and smart content placement, you'll rank higher for specific searches in your area. The enhanced lead generation system and professional presentation help you compete with larger companies while the rank tracking keeps you informed of your progress.`;
}

function generateBasicBusinessExamples(dynamicContent: DynamicProposalContent): string[] {
  return [
    "Professional 5-page website that builds immediate credibility with potential customers",
    "Easy content creation through the GetFound app - just take photos and record voice notes after each job",
    "Filterable portfolio that lets customers see examples of your specific services"
  ];
}

function generatePremiumBusinessExamples(dynamicContent: DynamicProposalContent): string[] {
  return [
    "Dedicated service pages that rank individually in search results, bringing in more targeted leads",
    "Advanced SEO strategy that helps you compete with larger companies in your market",
    "Smart AI content placement that automatically strengthens your most important service pages",
    "Rank tracking dashboard to monitor your search engine performance and ROI"
  ];
}

function extractBusinessNameFromContent(dynamicContent: DynamicProposalContent): string {
  // Extract business name from project title or use fallback
  const title = dynamicContent.projectTitle || '';
  const match = title.match(/for (.+)$/);
  return match ? match[1] : 'your business';
}
