import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { nanoid } from 'nanoid';
import { createDefaultEnhancedProposal } from '@/types/enhanced-proposal-schema';
import { generateProposalReferenceNumber } from '@/lib/reference-number-generator';
import { pydanticAIService } from '@/lib/pydantic-ai-service';

/**
 * Creates enhanced packages with AI-generated content and detailed descriptions
 */
function createEnhancedPackagesFromAI(aiProposalData: any, businessName: string) {
  const recommendedPackage = aiProposalData.packageRecommendation?.recommended || 'basic';

  return [
    {
      name: 'Basic "GetFound" Package',
      description: `Designed to establish a strong online foundation for ${businessName} and leverage our unique content app. This package provides everything you need to create a professional web presence that showcases your work and makes it easy for customers to find and contact you.`,
      features: aiProposalData.customizedFeatures?.basic || [
        `Custom 5-Page Professional Website designed specifically for ${businessName}`,
        `GetFound Mobile App Integration - easily capture and showcase your completed projects`,
        `Filterable Service Portfolio - let customers see examples of your specific work`,
        `Mobile-Responsive Design ensuring your site looks perfect on all devices`,
        `Basic SEO Setup & Optimization to help customers find ${businessName} online`,
        `Professional Quote Request Forms streamlined for your services`,
        `Website Hosting & Maintenance - we handle all the technical details`,
        `Google Business Profile Review & Optimization recommendations`
      ],
      setupFee: '$499',
      monthlyFee: '$100',
      isRecommended: recommendedPackage === 'basic',
      howItHelps: `This package provides ${businessName} with a professional website that builds credibility and makes it easy for customers to request quotes. The GetFound app integration means you can effortlessly showcase completed projects, building trust with potential customers who can see your actual work. The mobile-responsive design ensures your site looks great on all devices, and basic SEO helps customers find you online.`,
      businessExamples: [
        "Professional 5-page website that builds immediate credibility with potential customers",
        "Easy content creation through the GetFound app - just take photos and record voice notes after each job",
        "Filterable portfolio that lets customers see examples of your specific services"
      ],
      paymentBreakdown: {
        upfront: '$499',
        monthly: '$100/month',
        amortized: '$141.58/month for 12 months, then $100/month'
      }
    },
    {
      name: 'Premium "GetFound" SEO & Growth Package',
      description: `Our most comprehensive solution, designed for maximum visibility, targeted SEO, and enhanced lead generation for ${businessName}. This package strategically structures your website content to rank higher in search results and attract your ideal customers.`,
      features: aiProposalData.customizedFeatures?.premium || [
        `Comprehensive 10-15+ Page Website with dedicated service pages for ${businessName}`,
        `Advanced SEO Strategy with keyword research specific to your market area`,
        `Smart AI Content Builder - automatically places your GetFound app content on relevant service pages`,
        `Individual Service Pages that rank separately in search results`,
        `Enhanced Lead Generation System with specialized quote forms`,
        `Keyword Rank Tracking Dashboard (launching September 2025)`,
        `Comprehensive Google Business Profile Management and optimization`,
        `Local SEO Optimization targeting customers in your service area`,
        `Professional Content Strategy designed for your specific business type`,
        `Advanced Analytics and Performance Monitoring`
      ],
      setupFee: '$2,999',
      monthlyFee: '$150',
      isRecommended: recommendedPackage === 'premium',
      howItHelps: `This comprehensive package transforms ${businessName} into a search engine powerhouse. With dedicated pages for each service, advanced SEO strategy, and smart content placement, you'll rank higher for specific searches in your area. The enhanced lead generation system and professional presentation help you compete with larger companies while the rank tracking keeps you informed of your progress.`,
      businessExamples: [
        "Dedicated service pages that rank individually in search results, bringing in more targeted leads",
        "Advanced SEO strategy that helps you compete with larger companies in your market",
        "Smart AI content placement that automatically strengthens your most important service pages",
        "Rank tracking dashboard to monitor your search engine performance and ROI"
      ],
      paymentBreakdown: {
        upfront: '$2,999',
        monthly: '$150/month',
        amortized: '$399.92/month for 12 months, then $150/month'
      }
    }
  ];
}

export async function POST(request: Request) {
  try {
    console.log("Create proposal API called");
    
    // Create a Supabase client that uses cookies (better for server components)
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Get current user with the cookie-based client
    const { data: userData, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.error('Auth error:', userError);
      // Add more detail to the error response
      return NextResponse.json({ 
        error: 'Authentication failed',
        message: userError.message,
        code: userError.code || 'AUTH_ERROR',
        hint: 'Please make sure you are logged in. Try going to /debug/login-helper to fix your session.'
      }, { status: 401 });
    }
    
    if (!userData.user) {
      console.error('No user found in session');
      return NextResponse.json({ 
        error: 'User not authenticated',
        hint: 'No user found in session. Please log in through /login or try /debug/login-helper.'
      }, { status: 401 });
    }
    
    console.log('User authenticated:', userData.user.email);
    
    // Parse the request body
    const body = await request.json();
    console.log('Received proposal data:', JSON.stringify(body, null, 2));
    
    // Validate required fields
    if (!body.client_name || !body.business_name || !body.business_type) {
      return NextResponse.json({ 
        error: 'Missing required fields',
        requiredFields: ['client_name', 'business_name', 'business_type']
      }, { status: 400 });
    }
    
    // Get the template data if a template_id was provided
    let templateData = null;
    if (body.template_id) {
      const { data: template, error: templateError } = await supabase
        .from('proposal_templates')
        .select('template_data')
        .eq('id', body.template_id)
        .single();
      
      if (templateError && templateError.code !== 'PGRST116') {
        console.error('Template fetch error:', templateError);
      } else if (template) {
        templateData = template.template_data;
      }
    }
    
    // If no template_id was provided or the template wasn't found, get the default template
    if (!templateData) {
      const { data: defaultTemplate, error: defaultTemplateError } = await supabase
        .from('proposal_templates')
        .select('template_data')
        .eq('is_default', true)
        .single();
      
      if (defaultTemplateError && defaultTemplateError.code !== 'PGRST116') {
        console.error('Default template fetch error:', defaultTemplateError);
      } else if (defaultTemplate) {
        templateData = defaultTemplate.template_data;
      } else {
        // If no default template exists, use the enhanced proposal schema
        templateData = createDefaultEnhancedProposal(
          body.client_name, 
          body.business_name, 
          body.business_type
        );
      }
    }
    
    // Generate a unique URL key for sharing
    const urlKey = nanoid(10);

    // Generate AI-powered proposal content if customer information is provided
    let aiProposalData = null;
    if (body.notes || body.challenges) {
      try {
        console.log('🤖 Generating AI-powered proposal content...');
        const customerInput = `
Business Name: ${body.business_name}
Business Type: ${body.business_type}
Client: ${body.client_name}

${body.challenges ? `Challenges/Situation: ${body.challenges}` : ''}
${body.notes ? `Additional Notes: ${body.notes}` : ''}
${body.target_audience ? `Target Audience: ${body.target_audience}` : ''}
${body.competitor_names ? `Competitors: ${body.competitor_names}` : ''}
${body.website_url ? `Current Website: ${body.website_url}` : ''}
        `.trim();

        aiProposalData = await pydanticAIService.generateProposal(customerInput);
        console.log('✅ AI proposal generated successfully');
      } catch (error) {
        console.error('❌ AI proposal generation failed:', error);
        // Continue with template-based approach
      }
    }

    // Create the proposal data combining AI content, template, and user input
    const proposalData = {
      client_name: body.client_name,
      business_name: body.business_name,
      business_type: body.business_type,
      reference_number: body.reference_number || await generateProposalReferenceNumber(),
      challenges: body.challenges || null,
      notes: body.notes || null,
      services: body.services || [],
      status: body.status || 'draft',
      created_by: userData.user.id,
      url_key: urlKey,
      // Create the enhanced proposal data format
      proposal_data: aiProposalData ? {
        // Use AI-generated content
        clientName: body.client_name,
        businessName: body.business_name,
        businessType: body.business_type,
        projectTitle: aiProposalData.projectTitle,
        clientGreeting: aiProposalData.clientGreeting || aiProposalData.acknowledgment,
        situationAnalysis: aiProposalData.situationAnalysis || aiProposalData.acknowledgment,
        goalsSection: aiProposalData.goalsSection || 'Based on our discussion, we understand your key objectives.',
        packageRecommendation: aiProposalData.packageRecommendation,
        questionsAndAnswers: aiProposalData.questionsAndAnswers,
        nextSteps: aiProposalData.nextSteps,
        // Generate enhanced packages with AI content
        packages: createEnhancedPackagesFromAI(aiProposalData, body.business_name),
        // Additional user-provided data
        challenges: body.challenges || null,
        targetAudience: body.target_audience || null,
        competitorNames: body.competitor_names || null,
        websiteUrl: body.website_url || null,
        services: body.services || [],
        createdAt: new Date().toISOString(),
        isAIGenerated: true
      } : {
        // Fallback to template-based approach
        ...templateData,
        // Ensure core client data is in the enhanced format
        clientName: body.client_name,
        businessName: body.business_name,
        businessType: body.business_type,
        // Additional user-provided data
        challenges: body.challenges || null,
        targetAudience: body.target_audience || null,
        competitorNames: body.competitor_names || null,
        websiteUrl: body.website_url || null,
        services: body.services || [],
        // Add timestamp
        createdAt: new Date().toISOString(),
        isAIGenerated: false,
        // If the template is in the old format with sections, extract packages
        packages: templateData.packages ||
                 (templateData.sections &&
                  templateData.sections.find(s => s.id === 'packages')?.packages) ||
                 createDefaultEnhancedProposal("", "", "").packages
      }
    };
    
    console.log('Creating proposal in database...');
    
    // Insert the proposal into the database
    const { data, error } = await supabase
      .from('proposals')
      .insert(proposalData)
      .select()
      .single();
    
    if (error) {
      console.error('Proposal creation error:', error);
      return NextResponse.json({ 
        error: 'Failed to create proposal',
        message: error.message,
        details: error
      }, { status: 500 });
    }
    
    console.log('Proposal created successfully, ID:', data.id);
    
    // Create a proposal_event record for the creation
    await supabase
      .from('proposal_events')
      .insert({
        proposal_id: data.id,
        event_type: 'created',
        event_data: {
          user_id: userData.user.id,
          user_email: userData.user.email
        }
      });
    
    return NextResponse.json({ 
      success: true,
      id: data.id,
      url_key: data.url_key
    });
  } catch (error: any) {
    console.error('Error creating proposal:', error);
    return NextResponse.json({ 
      error: 'Failed to create proposal',
      message: error.message,
      stack: error.stack
    }, { status: 500 });
  }
} 