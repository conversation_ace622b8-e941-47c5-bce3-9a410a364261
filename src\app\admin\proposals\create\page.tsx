'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { isAdmin } from '@/lib/auth';

// Business industry options
const INDUSTRY_OPTIONS = [
  { value: 'contractor', label: 'Contractor/Construction' },
  { value: 'restaurant', label: 'Restaurant/Cafe' },
  { value: 'retail', label: 'Retail/Shop' },
  { value: 'professional', label: 'Professional Services' },
  { value: 'healthcare', label: 'Healthcare/Medical' },
  { value: 'beauty', label: 'Beauty/Salon' },
  { value: 'fitness', label: 'Fitness/Gym' },
  { value: 'automotive', label: 'Automotive/Repair' },
  { value: 'home', label: 'Home Services' },
  { value: 'real_estate', label: 'Real Estate' },
  { value: 'dental', label: 'Dental' },
  { value: 'legal', label: 'Legal Services' },
  { value: 'accounting', label: 'Accounting/Financial' },
  { value: 'manufacturing', label: 'Manufacturing' },
  { value: 'education', label: 'Education/Training' },
  { value: 'event', label: 'Event Planning' },
  { value: 'landscaping', label: 'Landscaping/Lawn Care' },
  { value: 'cleaning', label: 'Cleaning Services' },
  { value: 'pet', label: 'Pet Services' },
  { value: 'other', label: 'Other' }
];

// Service options
const SERVICE_OPTIONS = [
  // Construction/Contractor
  { value: 'general_contracting', label: 'General Contracting', category: 'contractor' },
  { value: 'remodeling', label: 'Remodeling', category: 'contractor' },
  { value: 'roofing', label: 'Roofing', category: 'contractor' },
  { value: 'plumbing', label: 'Plumbing', category: 'contractor' },
  { value: 'electrical', label: 'Electrical', category: 'contractor' },
  { value: 'carpentry', label: 'Carpentry', category: 'contractor' },
  { value: 'painting', label: 'Painting', category: 'contractor' },
  { value: 'flooring', label: 'Flooring', category: 'contractor' },
  { value: 'landscaping', label: 'Landscaping', category: 'landscaping' },
  { value: 'lawn_care', label: 'Lawn Care', category: 'landscaping' },
  { value: 'tree_service', label: 'Tree Service', category: 'landscaping' },
  
  // Restaurants/Food
  { value: 'dining', label: 'Dining Services', category: 'restaurant' },
  { value: 'takeout', label: 'Takeout', category: 'restaurant' },
  { value: 'delivery', label: 'Delivery', category: 'restaurant' },
  { value: 'catering', label: 'Catering', category: 'restaurant' },
  { value: 'coffee', label: 'Coffee Shop', category: 'restaurant' },
  { value: 'bakery', label: 'Bakery', category: 'restaurant' },
  
  // Retail
  { value: 'clothing', label: 'Clothing & Apparel', category: 'retail' },
  { value: 'electronics', label: 'Electronics', category: 'retail' },
  { value: 'furniture', label: 'Furniture', category: 'retail' },
  { value: 'specialty', label: 'Specialty Goods', category: 'retail' },
  { value: 'grocery', label: 'Grocery', category: 'retail' },
  
  // Professional Services
  { value: 'consulting', label: 'Consulting', category: 'professional' },
  { value: 'marketing', label: 'Marketing', category: 'professional' },
  { value: 'graphic_design', label: 'Graphic Design', category: 'professional' },
  { value: 'photography', label: 'Photography', category: 'professional' },
  { value: 'legal_services', label: 'Legal Services', category: 'legal' },
  { value: 'accounting', label: 'Accounting', category: 'accounting' },
  { value: 'insurance', label: 'Insurance', category: 'professional' },
  
  // Healthcare
  { value: 'medical_practice', label: 'Medical Practice', category: 'healthcare' },
  { value: 'chiropractic', label: 'Chiropractic', category: 'healthcare' },
  { value: 'dentistry', label: 'Dentistry', category: 'dental' },
  { value: 'therapy', label: 'Therapy Services', category: 'healthcare' },
  
  // Beauty/Wellness
  { value: 'salon', label: 'Hair Salon', category: 'beauty' },
  { value: 'spa', label: 'Spa Services', category: 'beauty' },
  { value: 'barber', label: 'Barber Shop', category: 'beauty' },
  { value: 'nail_care', label: 'Nail Care', category: 'beauty' },
  { value: 'massage', label: 'Massage Therapy', category: 'beauty' },
  
  // Fitness
  { value: 'gym', label: 'Gym/Fitness Center', category: 'fitness' },
  { value: 'personal_training', label: 'Personal Training', category: 'fitness' },
  { value: 'yoga', label: 'Yoga Studio', category: 'fitness' },
  { value: 'pilates', label: 'Pilates', category: 'fitness' },
  
  // Automotive
  { value: 'auto_repair', label: 'Auto Repair', category: 'automotive' },
  { value: 'oil_change', label: 'Oil Change', category: 'automotive' },
  { value: 'tire_service', label: 'Tire Service', category: 'automotive' },
  { value: 'detailing', label: 'Auto Detailing', category: 'automotive' },
  { value: 'body_shop', label: 'Body Shop', category: 'automotive' },
  
  // Home Services
  { value: 'cleaning', label: 'Cleaning Services', category: 'cleaning' },
  { value: 'maid_service', label: 'Maid Service', category: 'cleaning' },
  { value: 'carpet_cleaning', label: 'Carpet Cleaning', category: 'cleaning' },
  { value: 'hvac', label: 'HVAC', category: 'home' },
  { value: 'pest_control', label: 'Pest Control', category: 'home' },
  { value: 'appliance_repair', label: 'Appliance Repair', category: 'home' },
  
  // Real Estate
  { value: 'real_estate_sales', label: 'Real Estate Sales', category: 'real_estate' },
  { value: 'property_management', label: 'Property Management', category: 'real_estate' },
  { value: 'home_inspection', label: 'Home Inspection', category: 'real_estate' },
  
  // Pet Services
  { value: 'pet_grooming', label: 'Pet Grooming', category: 'pet' },
  { value: 'veterinary', label: 'Veterinary', category: 'pet' },
  { value: 'pet_boarding', label: 'Pet Boarding', category: 'pet' },
  { value: 'dog_walking', label: 'Dog Walking', category: 'pet' },
  
  // General/Other
  { value: 'other', label: 'Other', category: 'other' }
];

function CreateProposalContent() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // Removed unused state variables since we simplified to one input
  
  // Simplified form data - just one input field
  const [formData, setFormData] = useState({
    customerInput: ''
  });

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { value } = e.target;
    setFormData(prev => ({ ...prev, customerInput: value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    
    try {
      // Generate a sequential reference number
      const referenceResponse = await fetch('/api/generate-reference-number', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      let referenceNumber;
      if (referenceResponse.ok) {
        const refData = await referenceResponse.json();
        referenceNumber = refData.referenceNumber;
      } else {
        // Fallback to timestamp-based if API fails
        referenceNumber = `GF-${new Date().getFullYear()}-${Date.now().toString().slice(-4)}`;
      }
      
      // Create proposal object with just customer input
      const proposalData = {
        customer_input: formData.customerInput,
        reference_number: referenceNumber,
        status: 'draft'
      };
      
      // Submit to API
      const response = await fetch('/api/create-proposal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(proposalData),
        credentials: 'include' // Important: this ensures cookies are sent with the request
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response:', errorData);
        throw new Error(errorData.message || 'Failed to create proposal');
      }
      
      const data = await response.json();
      
      // Redirect to the proposal editor page
      router.push(`/admin/proposals/edit/${data.id}`);
    } catch (error: any) {
      console.error('Error creating proposal:', error);
      setError(error.message || 'Failed to create proposal. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Create New AI-Powered Proposal</h1>
        <p className="mt-1 text-sm text-gray-600">
          Simply paste or type your client's information and requirements. Our AI will automatically analyze it and create a professional, customized proposal.
        </p>
      </div>
      
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-600 rounded-md p-4">
          {error}
        </div>
      )}
      
      <div className="bg-white shadow rounded-lg p-6">
        <form onSubmit={handleSubmit}>
          <div className="space-y-6">
            {/* Single Input Section */}
            <div>
              <h2 className="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">Client Needs & Requirements</h2>
              <div>
                <label htmlFor="customerInput" className="block text-sm font-medium text-gray-700 mb-3">
                  Client Information & Requirements *
                </label>
                <div className="mb-3">
                  <button
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, customerInput: `Business: Lowery Finishing & Interiors
Contact: Quinn and Stuff
Business Type: Contractor specializing in interior finishing

Current Situation:
- Small contracting business focusing on interior finishing work
- Serves both homeowners and builders
- Currently has basic online presence but needs improvement
- Looking to enhance credibility and make it easier for customers to contact them

Goals & Challenges:
- Want to target specific services to both homeowners and builders
- Need a professional website that showcases their work
- Want to enhance credibility in the market
- Need easier way for customers to find and contact them
- Looking for optimized Google listing to improve local visibility

Services Offered:
- Interior finishing and trim work
- Custom millwork and cabinetry
- Flooring installation
- Painting and staining
- General interior contracting

Target Customers:
- Homeowners doing renovations
- Builders needing finishing contractors
- Property managers
- Real estate investors

Current Website: Basic or none
Budget: Looking for professional solution that provides good ROI
Timeline: Ready to start soon` }))}
                    className="text-sm text-blue-600 hover:text-blue-800 mb-2"
                  >
                    📋 Load Lowery Finishing Example
                  </button>
                </div>
                <textarea
                  id="customerInput"
                  name="customerInput"
                  value={formData.customerInput}
                  onChange={handleInputChange}
                  required
                  rows={12}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Paste or type all client information, business details, goals, challenges, and requirements here. The AI will automatically extract and organize this information into a professional proposal.

Example:
Business: ABC Landscaping
Contact: John Smith
Business Type: Landscaping contractor

Current Situation:
- Small landscaping business serving residential customers
- Has basic website that doesn't convert well
- Wants to attract more high-value customers

Goals:
- Increase online visibility
- Showcase completed projects
- Generate more qualified leads
- Compete with larger companies

Services: Lawn care, landscape design, hardscaping, maintenance..."
                />
                <p className="text-sm text-gray-500 mt-2">
                  The AI will automatically analyze this information and create a customized proposal with appropriate package recommendations, Q&A sections, and business-specific content.
                </p>
              </div>
            </div>
          </div>
          
          <div className="mt-8 flex justify-end">
            <button
              type="button"
              onClick={() => router.back()}
              className="mr-4 px-5 py-2.5 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className={`px-5 py-2.5 rounded-md font-medium ${
                isLoading 
                  ? 'bg-gray-400 text-gray-200 cursor-not-allowed' 
                  : 'bg-primary-600 text-white hover:bg-primary-700'
              }`}
            >
              {isLoading ? 'Creating...' : 'Create Proposal'}
            </button>
          </div>
        </form>
      </div>
    </>
  );
}

export default function CreateProposalPage() {
  const router = useRouter();
  
  useEffect(() => {
    const checkAccess = async () => {
      const adminAccess = await isAdmin();
      if (!adminAccess) {
        router.push('/dashboard');
      }
    };

    checkAccess();
  }, [router]);
  
  return (
    <DashboardLayout>
      <CreateProposalContent />
    </DashboardLayout>
  );
} 